import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  const hashedPassword = await bcrypt.hash('admin@123', 10);

  const admin = await prisma.adminUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
    },
  });

  console.log('Admin user created:', admin);

  // Create store items with specific IDs for spinner integration
  const storeItems = [
    {
      id: '8d40c969-3094-48af-a99a-a960faa1a380',
      name: '1 notebook',
      description: 'UEST branded notebook for students',
      coinPrice: 150,
      totalStock: 100,
      availableStock: 100,
      category: 'Stationery',
      status: 'ACTIVE'
    },
    {
      id: '6896cf87-98e8-4e2e-825b-619baf939e78',
      name: 'Keychain',
      description: 'UEST branded keychain',
      coinPrice: 200,
      totalStock: 50,
      availableStock: 50,
      category: 'Accessories',
      status: 'ACTIVE'
    },
    {
      id: '2616b6b1-bf89-4e14-955b-6bb44e6b9eab',
      name: 'uest super card',
      description: 'UEST smart card with special features',
      coinPrice: 500,
      totalStock: 25,
      availableStock: 25,
      category: 'Cards',
      status: 'ACTIVE'
    }
  ];

  for (const item of storeItems) {
    await prisma.storeItem.upsert({
      where: { id: item.id },
      update: {
        name: item.name,
        description: item.description,
        coinPrice: item.coinPrice,
        totalStock: item.totalStock,
        availableStock: item.availableStock,
        category: item.category,
        status: item.status as any
      },
      create: item as any
    });
  }

  console.log('Store items created/updated:', storeItems.length);
}

main()

  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(() => {
    prisma.$disconnect();
  });
