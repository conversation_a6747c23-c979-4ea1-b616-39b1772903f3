import prisma from '@/config/prismaClient';
import { getUestCoins } from './uestCoinTransactionService';
import { clearCart } from './cartService';
import { createNotification, createAdminNotification } from '@/utils/notifications';

export const checkProfileApprovalStatus = async (userId: string, userType: 'STUDENT' | 'CLASS') => {
  try {
    if (userType === 'STUDENT') {
      const studentProfile = await prisma.studentProfile.findUnique({
        where: { studentId: userId },
        select: {
          status: true,
          studentId: true,
          medium: true,
          classroom: true,
          birthday: true,
          school: true,
          address: true
        }
      });

      if (!studentProfile) {
        return {
          isApproved: false,
          status: 'PROFILE_NOT_FOUND',
          message: 'Please complete your profile first before making purchases.'
        };
      }

      const isProfileComplete = !!(
        studentProfile.medium &&
        studentProfile.classroom &&
        studentProfile.birthday &&
        studentProfile.school &&
        studentProfile.address
      );

      if (!isProfileComplete) {
        return {
          isApproved: false,
          status: 'PROFILE_INCOMPLETE',
          message: 'Please complete your profile (medium, classroom, birthday, school, address) before making purchases.'
        };
      }

      const isApproved = studentProfile.status === 'APPROVED';

      return {
        isApproved,
        status: studentProfile.status,
        message: isApproved
          ? 'Profile is approved'
          : studentProfile.status === 'PENDING'
            ? 'Your profile is under review. Please wait for admin approval.'
            : studentProfile.status === 'REJECTED'
              ? 'Your profile was rejected. Please update your profile and resubmit.'
              : 'Please complete your profile verification.'
      };
    } else if (userType === 'CLASS') {
      const classData = await prisma.classes.findUnique({
        where: { id: userId },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          contactNo: true,
          isVerified: true
        }
      });

      if (!classData) {
        return {
          isApproved: false,
          status: 'CLASS_NOT_FOUND',
          message: 'Class account not found. Please contact support.'
        };
      }

      const isBasicInfoComplete = !!(
        classData.firstName &&
        classData.lastName &&
        classData.email &&
        classData.contactNo &&
        classData.isVerified
      );

      if (!isBasicInfoComplete) {
        return {
          isApproved: false,
          status: 'CLASS_PROFILE_INCOMPLETE',
          message: 'Please complete your class profile and verify your email before making purchases.'
        };
      }

      const classStatus = await prisma.classesStatus.findUnique({
        where: { classId: userId },
        select: { status: true, classId: true }
      });

      if (!classStatus) {
        return {
          isApproved: false,
          status: 'STATUS_NOT_FOUND',
          message: 'Please submit your class profile for approval first.'
        };
      }

      const isApproved = classStatus.status === 'APPROVED';

      return {
        isApproved,
        status: classStatus.status,
        message: isApproved
          ? 'Profile is approved'
          : classStatus.status === 'PENDING'
            ? 'Your class profile is under review. Please wait for admin approval.'
            : classStatus.status === 'REJECTED'
              ? 'Your class profile was rejected. Please update your profile and resubmit.'
              : 'Please complete your class profile verification.'
      };
    }

    return {
      isApproved: false,
      status: 'INVALID_USER_TYPE',
      message: 'Invalid user type provided.'
    };
  } catch (error) {
    console.error('Error checking profile approval status:', error);
    return {
      isApproved: false,
      status: 'ERROR',
      message: 'Unable to verify profile status. Please try again later.'
    };
  }
};

interface CartItem {
  id: string;
  name: string;
  coinPrice: number;
  quantity: number;
}

interface PurchaseData {
  userId: string;
  userType: 'STUDENT' | 'CLASS';
  cartItems: CartItem[];
  totalCoins: number;
}

export const processPurchase = async (data: PurchaseData) => {
  const { userId, userType, cartItems, totalCoins } = data;

  try {
    const approvalStatus = await checkProfileApprovalStatus(userId, userType);
    if (!approvalStatus.isApproved) {
      return {
        success: false,
        error: 'PROFILE_NOT_APPROVED',
        message: approvalStatus.message,
        status: approvalStatus.status
      };
    }
    const result = await prisma.$transaction(async (tx) => {
      const userCoins = await getUestCoins(userId, userType);
      if (!userCoins || userCoins.coins < totalCoins) {
        return {
          success: false,
          error: 'Insufficient coins balance'
        };
      }

      for (const cartItem of cartItems) {
        const storeItem = await tx.storeItem.findUnique({
          where: { id: cartItem.id }
        });

        if (!storeItem) {
          return {
            success: false,
            error: `Store item not found: ${cartItem.name} (ID: ${cartItem.id})`
          };
        }

        if (storeItem.status !== 'ACTIVE') {
          return {
            success: false,
            error: `Item ${cartItem.name} is not available`
          };
        }

        if (storeItem.availableStock < cartItem.quantity) {
          return {
            success: false,
            error: `Insufficient stock for ${cartItem.name}. Available: ${storeItem.availableStock}, Requested: ${cartItem.quantity}`
          };
        }

        if (storeItem.coinPrice !== cartItem.coinPrice) {
          return {
            success: false,
            error: `Price mismatch for ${cartItem.name}`
          };
        }
      }

      let buyerName = '';

      if (userType === 'STUDENT') {
        const student = await tx.student.findUnique({
          where: { id: userId },
          select: { id: true, firstName: true, lastName: true }
        });

        if (!student) {
          return {
            success: false,
            error: 'Student not found'
          };
        }
        buyerName = `${student.firstName} ${student.lastName}`.trim();
      } else if (userType === 'CLASS') {
        const classUser = await tx.classes.findUnique({
          where: { id: userId },
          select: { id: true, firstName: true, lastName: true }
        });

        if (!classUser) {
          return {
            success: false,
            error: 'Class not found'
          };
        }
        buyerName = `${classUser.firstName} ${classUser.lastName}`.trim();
      }

      const orderIds = [];

      for (const cartItem of cartItems) {
        const itemTotalCoins = cartItem.coinPrice * cartItem.quantity;

        const order = await tx.storeOrder.create({
          data: {
            modelId: userId,
            modelType: userType,
            itemId: cartItem.id,
            itemName: cartItem.name,
            itemPrice: cartItem.coinPrice,
            quantity: cartItem.quantity,
            totalCoins: itemTotalCoins,
            status: 'PENDING'
          }
        });

        orderIds.push(order.id);

      }

      await tx.uestCoins.update({
        where: {
          modelId_modelType: {
            modelId: userId,
            modelType: userType
          }
        },
        data: {
          coins: {
            decrement: totalCoins
          }
        }
      });

      await tx.uestCoinTransaction.create({
        data: {
          modelId: userId,
          modelType: userType,
          amount: totalCoins,
          type: 'DEBIT',
          reason: `Store purchase - ${orderIds.length} items (Order #${orderIds[0].slice(-8)})`
        }
      });

      return { orderIds, firstOrderId: orderIds[0], buyerName };
    });

    if (result.success === false) {
      return {
        success: false,
        error: result.error
      };
    }

    await clearCart(userId, userType);

    const buyerName = result.buyerName || `${userType === 'STUDENT' ? 'Student' : 'Class'}`;

    try {
      await createNotification({
        userId,
        userType,
        type: userType === 'STUDENT' ? 'STUDENT_STORE_PURCHASE' : 'CLASS_STORE_PURCHASE',
        title: 'Store Purchase Successful! 🛒',
        message: `Your order has been placed successfully. Total: ${totalCoins} coins. Order ID: ${result.firstOrderId?.slice(-8) || 'N/A'}`,
        data: {
          orderId: result.firstOrderId,
          orderIds: result.orderIds,
          totalCoins,
          cartItems
        }
      });

      await createAdminNotification({
        type: 'ADMIN_NEW_STORE_ORDER',
        title: `New Store Order - ${buyerName}`,
        message: `${buyerName} (${userType === 'STUDENT' ? 'Student' : 'Class'}) has placed a new store order worth ${totalCoins} coins.`,
        data: {
          orderId: result.firstOrderId,
          orderIds: result.orderIds,
          userId,
          userType,
          buyerName,
          totalCoins,
          cartItems
        }
      });

    } catch (error) {
      console.error('Error sending notifications:', error);
    }

    return {
      success: true,
      orderId: result.firstOrderId,
      orderIds: result.orderIds,
      message: 'Purchase completed successfully'
    };

  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Purchase failed'
    };
  }
};

export const getUserOrders = async (userId: string, userType: 'STUDENT' | 'CLASS') => {
  try {
    const orders = await prisma.storeOrder.findMany({
      where: {
        modelId: userId,
        modelType: userType
      },
      include: {
        item: true
      },
      orderBy: { createdAt: 'desc' }
    });

    return {
      success: true,
      data: orders
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Failed to fetch orders'
    };
  }
};

export const getOrderById = async (orderId: string, userId: string, userType: 'STUDENT' | 'CLASS') => {
  try {
    const order = await prisma.storeOrder.findFirst({
      where: {
        id: orderId,
        modelId: userId,
        modelType: userType
      },
      include: {
        item: true
      }
    });

    if (!order) {
      return {
        success: false,
        error: 'Order not found'
      };
    }

    return {
      success: true,
      data: order
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Failed to fetch order'
    };
  }
};

export const createSpinOrder = async (params: {
  modelId: string;
  modelType: 'STUDENT' | 'CLASS';
  itemId: string;
  itemName: string;
  rewardId?: string;
}) => {
  try {
    const { modelId, modelType, itemId, itemName, rewardId } = params;

    console.log(`Creating spin order for ${modelType} ${modelId}: ${itemName} (Store Item ID: ${itemId})`);

    // First, verify the store item exists and has stock
    const storeItem = await prisma.storeItem.findUnique({
      where: { id: itemId }
    });

    if (!storeItem) {
      return {
        success: false,
        error: `Store item not found for ${itemName}`,
      };
    }

    if (storeItem.availableStock <= 0) {
      return {
        success: false,
        error: `${itemName} is out of stock`,
      };
    }

    // Verify spin reward if rewardId is provided
    if (rewardId) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const spinReward = await prisma.spinRewardWinner.findFirst({
        where: {
          modelId,
          modelType,
          rewardId: rewardId,
          createdAt: {
            gte: today,
          },
        },
      });

      if (!spinReward) {
        return {
          success: false,
          error: `You haven't won ${itemName} today. Please spin the wheel first!`,
        };
      }
    }

    // Create order and update stock in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the order
      const order = await tx.storeOrder.create({
        data: {
          modelId,
          modelType,
          itemId: itemId, // Use actual store item ID
          itemName: storeItem.name,
          itemPrice: storeItem.coinPrice,
          quantity: 1,
          totalCoins: 0, // Free for spin rewards
          status: 'PENDING',
        },
      });

      // Update store item stock
      await tx.storeItem.update({
        where: { id: itemId },
        data: {
          availableStock: {
            decrement: 1
          }
        }
      });

      return order;
    });

    console.log(`Spin order created successfully: ${result.id} for ${storeItem.name}`);

    // Create user notification
    await createNotification({
      userId: modelId,
      userType: modelType === 'STUDENT' ? 'STUDENT' : 'CLASS',
      type: modelType === 'STUDENT' ? 'STUDENT_STORE_PURCHASE' : 'CLASS_STORE_PURCHASE',
      title: `🎁 You won ${storeItem.name}!`,
      message: `Congratulations! You won ${storeItem.name} from the spin. Your order has been placed and will be processed soon.`,
      data: {
        orderId: result.id,
        itemName: storeItem.name,
        itemPrice: storeItem.coinPrice,
        quantity: 1,
        totalCoins: 0,
        source: 'SPIN_WHEEL',
        itemDetails: storeItem,
        status: 'PENDING',
      },
    });

    // Create admin notification
    await createAdminNotification({
      type: 'ADMIN_NEW_STORE_ORDER',
      title: `New Spin Reward Order: ${storeItem.name}`,
      message: `User ${modelId} (${modelType}) won ${storeItem.name} from spin wheel. Order ID: ${result.id.slice(-8)}`,
      data: {
        orderId: result.id,
        userId: modelId,
        userType: modelType,
        itemName: storeItem.name,
        itemPrice: storeItem.coinPrice,
        quantity: 1,
        totalCoins: 0,
        source: 'SPIN_WHEEL',
        itemDetails: storeItem,
        category: storeItem.category,
        status: 'PENDING',
        stockRemaining: storeItem.availableStock - 1,
      },
    });

    return {
      success: true,
      data: {
        order: result,
        itemDetails: storeItem,
        message: `Order created for ${storeItem.name}`,
        stockRemaining: storeItem.availableStock - 1,
      },
    };
  } catch (error: any) {
    console.error('Error creating spin order:', error);
    return {
      success: false,
      error: error.message || 'Failed to create spin order',
    };
  }
};
