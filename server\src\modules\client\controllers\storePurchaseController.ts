import { Request, Response } from 'express';
import { processPurchase, getUserOrders, getOrderById, createSpinOrder } from '../services/storePurchaseService';
import { sendSuccess, sendError } from '@/utils/response';

interface AuthenticatedRequest extends Request {
  authenticatedUser?: {
    id: string;
    userType: 'STUDENT' | 'CLASS';
    contactNo: string;
  };
}

export const purchaseItems = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const authenticatedUser = req.authenticatedUser;
    if (!authenticatedUser) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const { cartItems, totalCoins } = req.body;

    if (!cartItems || !Array.isArray(cartItems) || cartItems.length === 0) {
      sendError(res, 'Cart items are required', 400);
      return;
    }

    if (!totalCoins || totalCoins <= 0) {
      sendError(res, 'Invalid total coins amount', 400);
      return;
    }

    for (const item of cartItems) {
      if (!item.id || !item.name || !item.coinPrice || !item.quantity) {
        sendError(res, 'Invalid cart item structure', 400);
        return;
      }
    }

    const result = await processPurchase({
      userId: authenticatedUser.id,
      userType: authenticatedUser.userType,
      cartItems,
      totalCoins
    });

    if (!result.success) {
      const errorResult = result as { success: false; error: string };
      sendError(res, errorResult.error, 400);
      return;
    }

    sendSuccess(res, result, 'Purchase completed successfully', 201);
  } catch (error: any) {
    sendError(res, error.message || 'Purchase failed', 500);
  }
};

export const getMyOrders = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const authenticatedUser = req.authenticatedUser;
    if (!authenticatedUser) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const result = await getUserOrders(authenticatedUser.id, authenticatedUser.userType);
    if (!result.success) {
      sendError(res, result.error, 500);
      return;
    }

    sendSuccess(res, result.data, 'Orders retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to fetch orders', 500);
  }
};

export const getOrderDetails = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const authenticatedUser = req.authenticatedUser;
    if (!authenticatedUser) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const { orderId } = req.params;
    if (!orderId) {
      sendError(res, 'Order ID is required', 400);
      return;
    }

    const result = await getOrderById(orderId, authenticatedUser.id, authenticatedUser.userType);
    if (!result.success) {
      sendError(res, result.error, 404);
      return;
    }

    sendSuccess(res, result.data, 'Order details retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to fetch order details', 500);
  }
};

export const createSpinOrderController = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const authenticatedUser = req.authenticatedUser;
    if (!authenticatedUser) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const { itemId, itemName, rewardId } = req.body;

    if (!itemId || !itemName) {
      sendError(res, 'Missing required fields: itemId and itemName', 400);
      return;
    }

    const result = await createSpinOrder({
      modelId: authenticatedUser.id,
      modelType: authenticatedUser.userType,
      itemId,
      itemName,
      rewardId, // Pass rewardId for verification
    });

    if (!result.success) {
      sendError(res, result.error, 400);
      return;
    }

    sendSuccess(res, result.data, 'Spin order created successfully', 201);
  } catch (error: any) {
    sendError(res, error.message || 'Failed to create spin order', 500);
  }
};
