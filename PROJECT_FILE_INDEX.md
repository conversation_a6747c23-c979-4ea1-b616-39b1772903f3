# UEST Application - Comprehensive File Index

## Project Overview
The UEST application is a multi-component educational platform consisting of:
- **Client Application** (Next.js) - Main student-facing application
- **Admin Panel** (Next.js) - Administrative dashboard
- **Server** (Node.js/TypeScript) - Backend API server
- **Classes Dashboard** (Laravel/PHP) - Class management system

## Project Structure

```
uest-app/
├── client/                 # Next.js client application
├── admin/                  # Next.js admin panel
├── server/                 # Node.js/TypeScript backend
├── classes-dashboard/      # Laravel class management
├── package.json           # Root package configuration
└── node_modules/          # Root dependencies
```

---

## 1. SERVER (Node.js/TypeScript Backend)

### Core Configuration
- `server/src/index.ts` - Main server entry point
- `server/package.json` - Server dependencies
- `server/tsconfig.json` - TypeScript configuration
- `server/eslint.config.mjs` - ESLint configuration

### Database & Configuration
- `server/src/config/prismaClient.ts` - Database client configuration
- `server/src/config/firebase.ts` - Firebase configuration
- `server/src/prisma/schema.prisma` - Database schema
- `server/src/prisma/seed.ts` - Database seeding
- `server/src/prisma/constantSeed.ts` - Constants seeding
- `server/src/prisma/spinRewardSeed.ts` - Spin rewards seeding
- `server/src/prisma/migrations/` - Database migrations

### Authentication & Middleware
- `server/src/middlewares/adminAuth.ts` - Admin authentication
- `server/src/middlewares/clientAuth.ts` - Client authentication
- `server/src/middlewares/studentAuth.ts` - Student authentication
- `server/src/middlewares/validateRequest.ts` - Request validation

### Admin Module
#### Routes
- `server/src/modules/admin/routes/authRoutes.ts` - Admin authentication
- `server/src/modules/admin/routes/studentRoutes.ts` - Student management
- `server/src/modules/admin/routes/examRoutes.ts` - Exam management
- `server/src/modules/admin/routes/examApplicationRoutes.ts` - Exam applications
- `server/src/modules/admin/routes/storeRoutes.ts` - Store management
- `server/src/modules/admin/routes/constantRoutes.ts` - Constants management
- `server/src/modules/admin/routes/activityLogRoutes.ts` - Activity logging
- `server/src/modules/admin/routes/adminChatRouter.ts` - Admin chat
- `server/src/modules/admin/routes/payoutRoutes.ts` - Payout management
- `server/src/modules/admin/routes/exportRoutes.ts` - Data export
- `server/src/modules/admin/routes/uwhizPriceRankingRoutes.ts` - UWhiz pricing
- `server/src/modules/admin/routes/uwhizCoinDeductionRoutes.ts` - Coin deduction
- `server/src/modules/admin/routes/getUwhizApplicantRoutes.ts` - UWhiz applicants
- `server/src/modules/admin/routes/getUwhizTerminatedStudentRoutes.ts` - Terminated students
- `server/src/modules/admin/routes/dailyQuizStreakNotificationRoutes.ts` - Quiz notifications

#### Controllers
- `server/src/modules/admin/controllers/` - Admin controllers

#### Services
- `server/src/modules/admin/services/` - Admin business logic

#### Requests & Interfaces
- `server/src/modules/admin/requests/` - Request validation schemas
- `server/src/modules/admin/interfaces/` - TypeScript interfaces
- `server/src/modules/admin/middleware/` - Admin-specific middleware

### Client Module
#### Routes
- `server/src/modules/client/routes/authRoutes.ts` - Client authentication
- `server/src/modules/client/routes/studentAuthRouter.ts` - Student authentication
- `server/src/modules/client/routes/studentProfileRoutes.ts` - Student profiles
- `server/src/modules/client/routes/classesRoutes.ts` - Classes management
- `server/src/modules/client/routes/classesProfileRoutes.ts` - Class profiles
- `server/src/modules/client/routes/classesThoughtRoutes.ts` - Class thoughts
- `server/src/modules/client/routes/classesTestimonialRoutes.ts` - Testimonials
- `server/src/modules/client/routes/classesBankPaymentDetailRoutes.ts` - Payment details
- `server/src/modules/client/routes/canClassesApplyRoutes.ts` - Class applications
- `server/src/modules/client/routes/cartRoutes.ts` - Shopping cart
- `server/src/modules/client/routes/chatRoutes.ts` - Chat functionality
- `server/src/modules/client/routes/coinRoutes.ts` - Coin system
- `server/src/modules/client/routes/spinRoutes.ts` - Spin wheel
- `server/src/modules/client/routes/storePurchaseRoutes.ts` - Store purchases
- `server/src/modules/client/routes/reviewsRoutes.ts` - Reviews system
- `server/src/modules/client/routes/referralRoutes.ts` - Referral system
- `server/src/modules/client/routes/notificationRoutes.ts` - Notifications
- `server/src/modules/client/routes/fcmTokenRoutes.ts` - FCM tokens
- `server/src/modules/client/routes/examMonitoringRoutes.ts` - Exam monitoring
- `server/src/modules/client/routes/examApplicantEmailRoutes.ts` - Exam emails
- `server/src/modules/client/routes/classViewLogRoutes.ts` - Class view logs
- `server/src/modules/client/routes/constantRoutes.ts` - Constants
- `server/src/modules/client/routes/uestCoinTransctionRoutes.ts` - Coin transactions
- `server/src/modules/client/routes/studentWishlistRoutes.ts` - Student wishlist
- `server/src/modules/client/routes/uwhizCertificateRoutes.ts` - UWhiz certificates
- `server/src/modules/client/routes/uwhizRankingRoutes.ts` - UWhiz rankings
- `server/src/modules/client/routes/uwhizStudentDetailRoutes.ts` - UWhiz student details
- `server/src/modules/client/routes/blogRoutes/blogRoutes.ts` - Blog system

#### Controllers & Services
- `server/src/modules/client/controllers/` - Client controllers
- `server/src/modules/client/services/` - Client business logic
- `server/src/modules/client/requests/` - Request validation schemas

### Utilities & Services
- `server/src/utils/response.ts` - Response utilities
- `server/src/utils/helper.ts` - Helper functions
- `server/src/utils/jwt.ts` - JWT utilities
- `server/src/utils/email.ts` - Email utilities
- `server/src/utils/emailTemplates.ts` - Email templates
- `server/src/utils/fileUtils.ts` - File utilities
- `server/src/utils/upload.ts` - Upload utilities
- `server/src/utils/notifications.ts` - Notification utilities
- `server/src/utils/activityLogger.ts` - Activity logging
- `server/src/utils/interface.ts` - Common interfaces

### Services
- `server/src/services/fcmTokenService.ts` - FCM token management
- `server/src/services/pushNotificationService.ts` - Push notifications

### Socket & Real-time
- `server/src/socket/socket.ts` - Socket.io configuration

### Types
- `server/src/types/express/` - Express type extensions

### Router
- `server/src/router/index.ts` - Main router configuration

---

## 2. CLIENT APPLICATION (Next.js Frontend)

### Core Configuration
- `client/src/app/layout.tsx` - Root layout
- `client/src/app/page.tsx` - Home page
- `client/src/app/globals.css` - Global styles
- `client/src/app/not-found.tsx` - 404 page
- `client/package.json` - Client dependencies
- `client/tsconfig.json` - TypeScript configuration
- `client/next.config.ts` - Next.js configuration
- `client/components.json` - Component configuration

### Pages & Routes
- `client/src/app/about/` - About page
- `client/src/app/blogs/` - Blog pages
- `client/src/app/careers/` - Career pages
- `client/src/app/checkout/` - Checkout process
- `client/src/app/class/` - Individual class pages
- `client/src/app/classes/` - Classes listing
- `client/src/app/classes-details/` - Class details
- `client/src/app/coins/` - Coin system pages
- `client/src/app/debug-orders/` - Order debugging
- `client/src/app/Leader-Board/` - Leaderboard
- `client/src/app/mock-exam-card/` - Mock exam cards
- `client/src/app/mock-exam-result/` - Mock exam results
- `client/src/app/mock-test/` - Mock tests
- `client/src/app/notifications/` - Notifications
- `client/src/app/orders/` - Order management
- `client/src/app/privacy-policy/` - Privacy policy
- `client/src/app/store/` - Store pages
- `client/src/app/student/` - Student dashboard
- `client/src/app/student-verify-otp/` - Student OTP verification
- `client/src/app/support/` - Support pages
- `client/src/app/terms-and-conditions/` - Terms and conditions
- `client/src/app/uwhiz/` - UWhiz main pages
- `client/src/app/uwhiz-details/` - UWhiz details
- `client/src/app/uwhiz-exam/` - UWhiz exams
- `client/src/app/uwhiz-info/` - UWhiz information
- `client/src/app/uwhiz-super-kids-result/` - UWhiz results
- `client/src/app/verified-classes/` - Verified classes
- `client/src/app/verify-email/` - Email verification
- `client/src/app/verify-otp/` - OTP verification
- `client/src/app/weekly-exam-card/` - Weekly exam cards
- `client/src/app/weekly-exam-result/` - Weekly exam results

### App Components
- `client/src/app-components/AppDatePicker.tsx` - Date picker component
- `client/src/app-components/AuthActions.tsx` - Authentication actions
- `client/src/app-components/AuthErrorHandler.tsx` - Auth error handling
- `client/src/app-components/BlogCard.tsx` - Blog card component
- `client/src/app-components/DynamicTable.tsx` - Dynamic table component
- `client/src/app-components/Footer.tsx` - Footer component
- `client/src/app-components/Header.tsx` - Header component
- `client/src/app-components/MonthYearPicker.tsx` - Month/year picker
- `client/src/app-components/NotificationBell.tsx` - Notification bell
- `client/src/app-components/ProfileCompletionIndicator.tsx` - Profile completion
- `client/src/app-components/RecentBlogs.tsx` - Recent blogs component
- `client/src/app-components/ReviewsSection.tsx` - Reviews section
- `client/src/app-components/SharedChat.tsx` - Shared chat component
- `client/src/app-components/SignUpSignIn.tsx` - Sign up/in component
- `client/src/app-components/StatsSection.tsx` - Statistics section
- `client/src/app-components/TestimonialSlider.tsx` - Testimonial slider

### UI Components
- `client/src/components/ExamCameraMonitoring.tsx` - Exam camera monitoring
- `client/src/components/MyOrders.tsx` - Orders component
- `client/src/components/SpinningWheel.tsx` - Spinning wheel component
- `client/src/components/ui/` - Shadcn UI components

### Services & API
- `client/src/services/AuthService.ts` - Authentication service
- `client/src/services/LeaderboardUserApi.ts` - Leaderboard API
- `client/src/services/WeeklyExamService.ts` - Weekly exam service
- `client/src/services/blogApi.ts` - Blog API
- `client/src/services/careerService.ts` - Career service
- `client/src/services/cartApi.ts` - Cart API
- `client/src/services/chatService.ts` - Chat service
- `client/src/services/classViewLogService.ts` - Class view logging
- `client/src/services/classesThoughtApi.ts` - Classes thought API
- `client/src/services/examApi.ts` - Exam API
- `client/src/services/examApplicantEmailApi.ts` - Exam applicant emails
- `client/src/services/examApplicationApi.ts` - Exam applications
- `client/src/services/examMonitoringApi.ts` - Exam monitoring
- `client/src/services/mock-exam-resultApi.ts` - Mock exam results
- `client/src/services/mockExamStreakApi.ts` - Mock exam streaks
- `client/src/services/notificationService.ts` - Notification service
- `client/src/services/questionBankApi.ts` - Question bank API
- `client/src/services/quizAttemptApi.ts` - Quiz attempts
- `client/src/services/quizTeminationApi.ts` - Quiz termination
- `client/src/services/quizTerminationLog.ts` - Quiz termination logs
- `client/src/services/referralApi.ts` - Referral API
- `client/src/services/reviewsApi.ts` - Reviews API
- `client/src/services/storeApi.ts` - Store API
- `client/src/services/storePurchaseApi.ts` - Store purchases
- `client/src/services/studentAuthServices.ts` - Student authentication
- `client/src/services/studentDetailServiceApi.ts` - Student details
- `client/src/services/studentWishlistServices.ts` - Student wishlist
- `client/src/services/uestCoinTransctionApi.ts` - UEST coin transactions
- `client/src/services/uwhizCertificateApi.ts` - UWhiz certificates
- `client/src/services/uwhizExamApi.ts` - UWhiz exams
- `client/src/services/uwhizMockExamApi.ts` - UWhiz mock exams
- `client/src/services/uwhizMockExamTerminationApi.ts` - UWhiz exam termination
- `client/src/services/uwhizPreventReattemptApi.ts` - UWhiz reattempt prevention
- `client/src/services/uwhizQuestionForStudentApi.ts` - UWhiz questions
- `client/src/services/uwhizRankingApi.ts` - UWhiz rankings
- `client/src/services/uwhizSaveExamAnswerApi.ts` - UWhiz exam answers

### Hooks & Utilities
- `client/src/hooks/AnalyticsProvider.tsx` - Analytics provider
- `client/src/hooks/getStudentId.tsx` - Student ID hook
- `client/src/hooks/use-mobile.ts` - Mobile detection hook
- `client/src/hooks/useFullScreen.ts` - Fullscreen hook

### Libraries & Configuration
- `client/src/lib/axios.ts` - Axios configuration
- `client/src/lib/gtag.ts` - Google Analytics
- `client/src/lib/helper.ts` - Helper functions
- `client/src/lib/types.ts` - Type definitions
- `client/src/lib/useAuth.ts` - Authentication hook
- `client/src/lib/utils.ts` - Utility functions
- `client/src/lib/constant/` - Constants

### State Management
- `client/src/store/hooks.ts` - Redux hooks
- `client/src/store/index.ts` - Store configuration
- `client/src/store/provider.tsx` - Store provider
- `client/src/store/slices/` - Redux slices
- `client/src/store/thunks/` - Redux thunks

### Providers
- `client/src/Providers/theme-provider.tsx` - Theme provider

---

## 3. ADMIN PANEL (Next.js Admin Dashboard)

### Core Configuration
- `admin/src/app/layout.tsx` - Admin layout
- `admin/src/app/page.tsx` - Admin home page
- `admin/src/app/globals.css` - Admin global styles
- `admin/src/app/not-found.tsx` - Admin 404 page
- `admin/package.json` - Admin dependencies
- `admin/tsconfig.json` - Admin TypeScript config
- `admin/next.config.ts` - Admin Next.js config
- `admin/components.json` - Admin component config

### Authentication
- `admin/src/app/login/` - Admin login pages

### Dashboard Routes
- `admin/src/app/(dashboard)/` - Main dashboard routes
- `admin/src/app/(examDashboard)/` - Exam dashboard routes

### Redux Store
- `admin/src/app/ReduxProvider.tsx` - Redux provider
- `admin/src/app/store.ts` - Redux store configuration
- `admin/src/app/examSlice.ts` - Exam slice

### App Components
- `admin/src/app-components/AdminChat.tsx` - Admin chat component
- `admin/src/app-components/AdminNotificationBell.tsx` - Admin notifications
- `admin/src/app-components/ConfirmDialog.tsx` - Confirmation dialog
- `admin/src/app-components/StudentProfileModal.tsx` - Student profile modal
- `admin/src/app-components/app-sidebar.tsx` - Application sidebar
- `admin/src/app-components/dataTable.tsx` - Data table component
- `admin/src/app-components/nav-main.tsx` - Main navigation
- `admin/src/app-components/nav-user.tsx` - User navigation
- `admin/src/app-components/pagination.tsx` - Pagination component
- `admin/src/app-components/site-header.tsx` - Site header

### UI Components
- `admin/src/components/CoinsTrancationModal.tsx` - Coins transaction modal
- `admin/src/components/ConfirmationDialog.tsx` - Confirmation dialog
- `admin/src/components/ExamForm.tsx` - Exam form component
- `admin/src/components/ExamTable.tsx` - Exam table component
- `admin/src/components/LevelPrenceManager.tsx` - Level preference manager
- `admin/src/components/PriceRankTable.tsx` - Price rank table
- `admin/src/components/StudentPhotoMonitoring.tsx` - Student photo monitoring
- `admin/src/components/SubjectPreferenceManager.tsx` - Subject preference manager
- `admin/src/components/TestimonialTable.tsx` - Testimonial table
- `admin/src/components/ui/` - Shadcn UI components

### Services & API
- `admin/src/services/activityLogApi.ts` - Activity log API
- `admin/src/services/auth.ts` - Authentication service
- `admin/src/services/blogApi.ts` - Blog API
- `admin/src/services/chatApi.ts` - Chat API
- `admin/src/services/classes-student.ts` - Classes student API
- `admin/src/services/classesApi.ts` - Classes API
- `admin/src/services/classesThoughtApi.ts` - Classes thought API
- `admin/src/services/constantsApi.ts` - Constants API
- `admin/src/services/dailyQuizResult.ts` - Daily quiz results
- `admin/src/services/email.ts` - Email service
- `admin/src/services/examApi.ts` - Exam API
- `admin/src/services/examApplicationApi.ts` - Exam application API
- `admin/src/services/examMonitoringApi.ts` - Exam monitoring API
- `admin/src/services/leaderbordApi.ts` - Leaderboard API
- `admin/src/services/mock-examApi.ts` - Mock exam API
- `admin/src/services/notificationService.ts` - Notification service
- `admin/src/services/questionApi.ts` - Question API
- `admin/src/services/questionBankApi.ts` - Question bank API
- `admin/src/services/quizTerminationLog.ts` - Quiz termination logs
- `admin/src/services/reviewsApi.ts` - Reviews API
- `admin/src/services/spinApi.ts` - Spin API
- `admin/src/services/storeApi.ts` - Store API
- `admin/src/services/storeOrderApi.ts` - Store order API
- `admin/src/services/studentApi.ts` - Student API
- `admin/src/services/testimonialApi.ts` - Testimonial API
- `admin/src/services/uwhiz-result.ts` - UWhiz results
- `admin/src/services/uwhizExamApplicantApi.ts` - UWhiz exam applicants
- `admin/src/services/uwhizLevelPrefrenceApi.ts` - UWhiz level preferences
- `admin/src/services/uwhizMockExamTerminationApi.ts` - UWhiz mock exam termination
- `admin/src/services/uwhizPriceRankApi.ts` - UWhiz price ranking
- `admin/src/services/uwhizQuizTerminationLogApi.ts` - UWhiz quiz termination logs
- `admin/src/services/uwhizSubjectPrefrenceApi.ts` - UWhiz subject preferences

### Libraries & Configuration
- `admin/src/lib/axios.ts` - Axios configuration
- `admin/src/lib/types.ts` - Type definitions
- `admin/src/lib/utils.ts` - Utility functions
- `admin/src/lib/validations/` - Validation schemas

### Hooks
- `admin/src/hooks/use-mobile.ts` - Mobile detection hook

---

## 4. CLASSES DASHBOARD (Laravel/PHP)

### Core Configuration
- `classes-dashboard/composer.json` - PHP dependencies
- `classes-dashboard/composer.lock` - Dependency lock file
- `classes-dashboard/package.json` - Node.js dependencies
- `classes-dashboard/package-lock.json` - Node.js lock file
- `classes-dashboard/artisan` - Laravel Artisan CLI
- `classes-dashboard/phpunit.xml` - PHPUnit configuration
- `classes-dashboard/vite.config.js` - Vite configuration
- `classes-dashboard/webpack.mix.js` - Laravel Mix configuration

### Application Structure
- `classes-dashboard/app/Console/Kernel.php` - Console kernel
- `classes-dashboard/app/Console/Commands/` - Artisan commands
- `classes-dashboard/app/Exceptions/Handler.php` - Exception handler
- `classes-dashboard/app/Http/Kernel.php` - HTTP kernel
- `classes-dashboard/app/Http/Helpers.php` - Helper functions

### Models
- `classes-dashboard/app/Models/Classes.php` - Classes model
- `classes-dashboard/app/Models/ClassesLogo.php` - Classes logo model

### Controllers
- `classes-dashboard/app/Http/Controllers/` - Web controllers
- `classes-dashboard/app/Http/APIControllers/` - API controllers

### Middleware
- `classes-dashboard/app/Http/Middleware/` - HTTP middleware

### Exports
- `classes-dashboard/app/Exports/CommonExport.php` - Common export functionality
- `classes-dashboard/app/Exports/SalaryReportExport.php` - Salary report export
- `classes-dashboard/app/Exports/TimeTableExport.php` - Timetable export

### Notifications
- `classes-dashboard/app/Notifications/NotifyUser.php` - User notifications
- `classes-dashboard/app/Notifications/PasswordResetMail.php` - Password reset emails
- `classes-dashboard/app/Notifications/UserActivityNotification.php` - Activity notifications

### Service Providers
- `classes-dashboard/app/Providers/AppServiceProvider.php` - Application service provider
- `classes-dashboard/app/Providers/AuthServiceProvider.php` - Authentication service provider
- `classes-dashboard/app/Providers/BroadcastServiceProvider.php` - Broadcasting service provider
- `classes-dashboard/app/Providers/EventServiceProvider.php` - Event service provider
- `classes-dashboard/app/Providers/RouteServiceProvider.php` - Route service provider
- `classes-dashboard/app/Providers/TenantServiceProvider.php` - Tenant service provider

### Configuration
- `classes-dashboard/config/` - Laravel configuration files

### Database
- `classes-dashboard/database/` - Database migrations, seeders, and factories

### Routes
- `classes-dashboard/routes/` - Route definitions

### Resources
- `classes-dashboard/resources/` - Views, assets, and language files

### Storage & Public
- `classes-dashboard/storage/` - File storage
- `classes-dashboard/public/` - Public assets

### Testing
- `classes-dashboard/tests/` - Test files

### Language Files
- `classes-dashboard/lang/` - Localization files

### Bootstrap
- `classes-dashboard/bootstrap/` - Application bootstrap files

### Modules
- `classes-dashboard/modules/` - Custom modules

### Vendor
- `classes-dashboard/vendor/` - Composer dependencies

---

## 5. ROOT CONFIGURATION

### Package Management
- `package.json` - Root package configuration with scripts for all components
- `package-lock.json` - Root dependency lock file
- `node_modules/` - Root dependencies (concurrently, husky)

### Development Scripts
- `npm run dev` - Start all components (client, admin, server, dashboard)
- `npm run client` - Start client application
- `npm run admin` - Start admin panel
- `npm run server` - Start Node.js server
- `npm run dashboard` - Start Laravel dashboard
- `npm run lint:all` - Lint all components
- `npm run prepare` - Husky git hooks setup

---

## 6. KEY FEATURES BY COMPONENT

### Server Features
- **Authentication**: Multi-role authentication (admin, client, student)
- **Exam System**: Comprehensive exam management with monitoring
- **UWhiz Platform**: Specialized exam platform with rankings and certificates
- **Chat System**: Real-time chat with Socket.io
- **Coin System**: Virtual currency and rewards
- **Store System**: E-commerce functionality
- **Notification System**: Push notifications and FCM
- **File Management**: Upload and file handling utilities
- **Activity Logging**: Comprehensive activity tracking
- **Email System**: Template-based email notifications

### Client Features
- **Student Dashboard**: Comprehensive student interface
- **Exam Taking**: Mock exams, weekly exams, UWhiz exams
- **Class Management**: Class enrollment and viewing
- **Store Integration**: Shopping cart and purchases
- **Coin System**: Earning and spending virtual currency
- **Chat System**: Student communication
- **Blog System**: Educational content
- **Leaderboards**: Performance tracking
- **Notifications**: Real-time updates
- **Profile Management**: Student profile completion

### Admin Features
- **Student Management**: Comprehensive student administration
- **Exam Administration**: Exam creation and monitoring
- **Content Management**: Blog and testimonial management
- **Store Management**: Product and order management
- **Analytics**: Activity logs and reporting
- **Communication**: Admin chat system
- **UWhiz Management**: Specialized platform administration
- **Notification Management**: Push notification control

### Classes Dashboard Features
- **Class Management**: Laravel-based class administration
- **Timetable Management**: Schedule creation and export
- **Salary Reports**: Financial reporting
- **Multi-tenant Support**: Tenant-based architecture
- **User Management**: Class-specific user administration

---

## 7. TECHNOLOGY STACK

### Frontend
- **Framework**: Next.js 14+ with TypeScript
- **Styling**: Tailwind CSS, Shadcn UI components
- **State Management**: Redux Toolkit
- **HTTP Client**: Axios
- **Real-time**: Socket.io client
- **Authentication**: JWT-based authentication
- **Forms**: React Hook Form with Zod validation

### Backend
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT tokens
- **Real-time**: Socket.io
- **File Upload**: Multer
- **Email**: Nodemailer
- **Push Notifications**: Firebase Cloud Messaging
- **Validation**: Zod schemas

### Classes Dashboard
- **Framework**: Laravel (PHP)
- **Database**: MySQL/PostgreSQL
- **Frontend Build**: Vite/Laravel Mix
- **Authentication**: Laravel Auth
- **Export**: Laravel Excel

### Development Tools
- **Linting**: ESLint
- **Git Hooks**: Husky
- **Process Management**: Concurrently
- **Testing**: PHPUnit (Laravel), Jest/Testing Library (Node.js)

---

## 8. FILE ORGANIZATION PATTERNS

### Naming Conventions
- **Routes**: `*Routes.ts` (e.g., `authRoutes.ts`)
- **Services**: `*Service.ts` or `*Api.ts`
- **Components**: PascalCase (e.g., `StudentProfileModal.tsx`)
- **Utilities**: camelCase (e.g., `helper.ts`)
- **Types**: `types.ts` or `interface.ts`

### Directory Structure Patterns
- **Modular Architecture**: Separate modules for admin and client
- **Feature-based Organization**: Related files grouped by feature
- **Layered Architecture**: Controllers, services, routes separated
- **Shared Utilities**: Common utilities in dedicated directories

This comprehensive index provides a complete overview of all files and their purposes within the UEST application ecosystem.
